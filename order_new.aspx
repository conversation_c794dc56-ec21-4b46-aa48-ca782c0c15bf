<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="order_new.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .password-content {
            position: fixed !important;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .password-content {
            z-index: 9991;
        }

        .action_button {
            background: #FE6225;
            color: #fff;
            width: 100%;
            padding: 16px;
            box-sizing: border-box;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            font-size: 14px;
            margin-top: 13px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .order_button_limit {
            background: #31394A !important;
            color: #F6E0C4 !important;
        }

        /* 收益轮播样式 */
        .earnings-carousel {
            background: linear-gradient(135deg, #fafbfc, #f1f3f4);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            overflow: hidden;
            position: relative;
            height: 180px;
            border: 1px solid #e8eaed;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .carousel-container {
            position: relative;
            height: 100%;
            overflow: hidden;
        }

        .carousel-track {
            position: absolute;
            width: 100%;
            transition: transform 0.8s ease-in-out;
        }

        .carousel-item {
            padding: 0px 10px;
            text-align: center;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .carousel-item-line1 {
            color: #495057;
            font-size: 13px;
            line-height: 1.4;
            font-weight: 500;
        }

        .carousel-item-username {
            color: #6c757d;
            font-weight: bold;
        }

        .carousel-item-amount {
            color: #fd7e14;
            font-weight: bold;
            font-size: 15px;
            text-shadow: 0 1px 2px rgba(253,126,20,0.3);
        }

        .carousel-item-line2 {
            color: #198754;
            font-weight: bold;
            font-size: 14px;
            margin-top: 4px;
            text-shadow: 0 1px 2px rgba(25,135,84,0.2);
        }

        .carousel-item-profit {
            font-size: 16px;
            color: #20c997;
        }
    </style>
    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>

    <script>
        $(function () {
            $('.main_button').on('click', function () {
                location.href = location.href;
            })
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; padding-bottom: 0;" class="order_page">


        <div style="margin-bottom: 20px;">
            <h1 style="margin-bottom: 3px; margin-left: -13px;font-size:19px">【企鹅Pay实力平台 负责全程担保】</h1>
            <div style="font-size: 14px; color: gray;">
                简单三步成为矿工，收益最高补贴2.5%，极速秒到账！
            </div>
        </div>

        <!-- 收益轮播效果 -->
        <div class="earnings-carousel">
            <div class="carousel-container">
                <div class="carousel-track">
                    <!-- 收益数据 - 每行一个项目 -->
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">张***</span> 完成 <span class="carousel-item-amount">99.99</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">1.88</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">李***</span> 完成 <span class="carousel-item-amount">188.88</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">3.77</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">王***</span> 完成 <span class="carousel-item-amount">299.99</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">5.99</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">陈***</span> 完成 <span class="carousel-item-amount">66.66</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">1.33</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">刘***</span> 完成 <span class="carousel-item-amount">399.99</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">7.99</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">赵***</span> 完成 <span class="carousel-item-amount">158.88</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">3.18</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">孙***</span> 完成 <span class="carousel-item-amount">88.88</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">1.78</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">周***</span> 完成 <span class="carousel-item-amount">266.66</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">5.33</span></div>
                    </div>
                    <div class="carousel-item">
                        <div class="carousel-item-line1">🎉 恭喜 <span class="carousel-item-username">吴***</span> 完成 <span class="carousel-item-amount">128.88</span> 一键任务</div>
                        <div class="carousel-item-line2">获得收益 <span class="carousel-item-profit">2.58</span></div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 收益轮播效果 - 逐行慢慢滚动
            $(document).ready(function() {
                var currentIndex = 0;
                var items = $('.carousel-item');
                var totalItems = items.length;
                var track = $('.carousel-track');
                var itemHeight = 60; // 每行高度（包含边距）
                var visibleRows = 3; // 可见行数
                var maxScrollIndex = totalItems - visibleRows; // 最大滚动索引

                function showNextItem() {
                    currentIndex++;

                    // 如果滚动到最后可见位置，直接跳回顶部
                    if (currentIndex > maxScrollIndex) {
                        currentIndex = 0;
                        track.css('transition', 'none'); // 临时禁用动画
                        track.css('transform', 'translateY(0px)');

                        // 重新启用动画（延迟一帧）
                        setTimeout(function() {
                            track.css('transition', 'transform 0.8s ease-in-out');
                        }, 50);
                    } else {
                        var translateY = -currentIndex * itemHeight;
                        track.css('transform', 'translateY(' + translateY + 'px)');
                    }
                }

                // 每3秒滚动一行
                setInterval(showNextItem, 3000);
            });
        </script>


        <style>
            .bz_main{
                text-align: center;
                color:#1a1a1a;
                font-size:18px;
            }
            .bz_title{
                /*color:#666;*/
                color:#1a1a1a;
            }
        </style>


    </div>


    <%if (uConfig.stcdata(pmlist["usertype"] + "_task_view") + "" != "hide")
        {
    %>

    <%-- <a style="text-decoration: none;" id="order_button" class="action_button order_page"><%="<label>"+(uConfig.stcdata("task_funcs").IndexOf("fzyj") != -1?"开始搬砖":"开始搬砖")+"</label>" + (pmlist["is_double"] + "" == "1" ? "<span style='color: #ee7d25;font-weight: bold;background: #000000e3;font-size: 12px;padding: 2px 10px;border-radius: 5px;margin-left: 6px;box-shadow: 2px 2px 8px #ebdc5f;'>        <svg t='1701338602094' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='9074' width='12' height='12' style='margin-right: 3px;'>            <path d='M859.995 460.963c-1.197 10.478-1.197 21.854 0 33.527v88.305c0 160.751-110.161 296.657-258.34 336.17v-52.982c0-49.693-40.112-89.805-89.505-89.805h-0.3c-49.394 0-89.805 40.112-89.805 89.805v52.982c-148.179-39.812-258.04-175.719-258.04-336.17V431.328c39.814 32.33 93.098 91.302 89.805 172.726 33.227-153.267 99.085-176.318 154.764-239.48 72.444-82.321 93.996-172.726 52.386-259.538C641.17 167.6 687.868 409.476 672.901 536.7c64.959-115.251 146.682-146.083 187.094-154.166v78.429z' fill='#FF7B15' p-id='9075'></path></svg>双倍收益</span>" : "") %></a> --%>

<%

        } %>



    <a href="task_onetouch.aspx" id="agetn_button" class="action_button order_page" style="text-decoration: none;">一键搬砖【自动挂机赚钱】</a>

    <%if (uConfig.stcdata("task_funcs").IndexOf("fzyj") != -1)
        {%>
    <%--<a onclick="javascript:alert('暂不可用')" id="A2" class="action_button order_page ">
        <label>一键搬砖</label></a>--%>

    <%--<a onclick="javascript:location.href='task_rules.aspx'" id="A1" class="action_button order_page ">
        <label>搬砖规则</label></a>--%>
    <%--<a onclick="javascript:location.href='task_rules.aspx?type=jrhhr'" id="A2" class="action_button order_page ">
        <label>加入合伙人</label></a>--%>

    <%}%>

    <%--<a onclick="$('#pop-cpt').show();" id="add_button" class="action_button order_page" style="background: #ffd1bf; color: #c7582d; font-weight: bold;">一键增加搬砖额度</a>--%>


    <style>
        thead {
            position: sticky; /*设置表格头部为粘性定位*/
            top: 0; /*设置表格头部固定在顶部*/
        }

        /*body {
            overflow: hidden;
        }*/

        #orderTop_menu > div {
            width: 50%;
            text-align: center;
            color: gray;
        }

            #orderTop_menu > div.active {
                color: #0268FB;
                font-weight: bold;
            }
    </style>




    <div class=" pg-select-items order_page" style="display: none;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;" id="task_list">


                <div style="display: flex; padding: 12px;" id="orderTop_menu">

                    <div class="active" data-page="index">
                        搬砖大厅
                        <%=(pmlist["is_double"] + ""=="1" ? "<span style='color: #ee7d25; font-weight: bold; background: #000000e3; font-size: 12px; padding: 2px 10px; border-radius: 5px; margin-left: 6px; box-shadow: 2px 2px 8px #ee7d25;'>        <svg t='1701338602094' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='9074' width='12' height='12' style='margin-right: 3px;'>            <path d='M859.995 460.963c-1.197 10.478-1.197 21.854 0 33.527v88.305c0 160.751-110.161 296.657-258.34 336.17v-52.982c0-49.693-40.112-89.805-89.505-89.805h-0.3c-49.394 0-89.805 40.112-89.805 89.805v52.982c-148.179-39.812-258.04-175.719-258.04-336.17V431.328c39.814 32.33 93.098 91.302 89.805 172.726 33.227-153.267 99.085-176.318 154.764-239.48 72.444-82.321 93.996-172.726 52.386-259.538C641.17 167.6 687.868 409.476 672.901 536.7c64.959-115.251 146.682-146.083 187.094-154.166v78.429z' fill='#FF7B15' p-id='9075'></path></svg>双倍收益</span>" : "") %>
                    </div>
                    <div data-page="list">
                        进行中
               
                    </div>

                </div>



                <div style="background: #f3f5f7; color: #493325; font-size: 15px; padding: 10px; font-weight: bold; margin-bottom: 3px; text-align: center;">
                    <%if (pmlist["show_type"] + "" == "amount")
                        {
                    %>今日剩余搬砖额度：<span id="u_current_amount"><%=positive_number((Convert.ToDouble(pmlist["amount"])+Convert.ToDouble(uConfig.gd(userdt,"share_reward_amount"))).ToString("0")) %></span><%
                                                                                                                 }
                                                                                                                 else
                                                                                                                 {
                    %>今日剩余搬砖次数：<span id="u_current_amount"><%=positive_number(Convert.ToDouble(pmlist["number"])+0) %></span><%
                                                 } %>&nbsp;&nbsp;冻结订单金额：<span id="u_current_freeze_amount"><%=uConfig.gnumber(userdt,"freeze_amount") %></span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                </div>


                <div class="orderlist_page" style="margin-bottom: 10px; display: none;">


                    <div style="flex-shrink: 0; display: flex; flex-direction: column; justify-content: center; line-height: 21px; color: blue; font-weight: bold; font-size: 14px; padding: 12px; margin: 10px; background: #eee; border-radius: 8px; margin-bottom: 0px;">



                        <div>
                            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>当前进行中的订单【<span id="task_order_number">0</span>单】
                        </div>


                        <div>
                            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>保持在线状态及时处理订单；
                        </div>


                        <div>
                            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>请再订单有效期内进行，订单回馈！
                        </div>

                        <div>
                            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>未及时回馈，佣金损失自行承担！
                        </div>

                        <div>
                            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>未完成订单请勿离开！避免资金亏损！
                        </div>

                    </div>

                </div>


                <div class="orderlist_page" style="background: rgb(231, 231, 231); color: rgb(202, 211, 199); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; display: none;">



                    <style>
                        #order_list {
                            width: 100%;
                            text-align: center;
                            font-size: 14px;
                        }

                            #order_list thead {
                                background: #f1f1f1;
                            }

                            #order_list th {
                                color: #5a5b5c;
                                padding: 8px 0;
                                background: #f1f1f1;
                            }

                            #order_list td {
                                padding: 10px 0;
                                width: 25%;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 100px;
                                text-align: center;
                            }
                    </style>



                    <div class="user_orderList" style="border-radius: 8px; text-align: center; color: rgb(154, 155, 152); font-weight: bold; font-size: 14px; margin: 10px;">
                    </div>


                </div>




                <div class="getOrder_page" style="background: rgb(241, 241, 241); color: rgb(0, 0, 0); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; display: block; padding: 0 10px;">



                    <table id="order_list">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>金额</th>
                                <th>佣金</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>








                </div>

                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>






        </div>

    </div>


    <div class=" pg-ontouch order_page" style="display: none;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="display: flex; background: #73C3CB; color: #fff; padding: 12px 0; font-weight: bold;">
                    <div>
                        <a class="ppreturn" style="height: 100%; width: 50px; display: flex; align-items: baseline; justify-content: center;" onclick="location.href='order_new.aspx'">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#fff" p-id="27858"></path></svg></a>
                    </div>

                    <div style="width: 100%; text-align: center;">一键挂单【自动搬砖】</div>

                    <div style="width: 102px;">
                        <a onclick="open_gdlist()">挂单记录</a>
                    </div>

                </div>

                <div style="overflow-y: auto;">

                    <div style="padding: 18px;">

                        <div style="margin-bottom: 18px; margin-top: 10px; display: flex;">
                            <div style="width: 130px">账户余额</div>
                            <span style="color: red;" class="user_amount"><%=uConfig.gnumber(userdt, "amount") %></span>
                            <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                        </div>

                        <div style="display: flex; font-weight: bold; color: #000; padding: 10px 0; align-items: center;">
                            <div style="width: 130px;">
                                输入挂单金额
                       
                            </div>
                            <div>
                                <input style="border-radius: 3px; border: 1px solid #73C3CB; padding: 8px; outline: none; font-weight: bold; text-align: center; box-shadow: 3px 2px 5px #73C3CB;" id="task_amount">
                            </div>
                        </div>

                        <div style="display: flex; font-weight: bold; color: #000; padding: 10px 0; align-items: center;">
                            <div style="width: 91px;">回款方式</div>
                            <div style="display: flex;">
                                <div style="border-radius: 3px; border: 1px solid #73C3CB; padding: 5px 8px; outline: none; font-weight: 500; text-align: center; display: flex; align-items: center; margin-right: 8px;">
                                    <svg t="1703606782646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5245" width="18" height="18">
                                        <path d="M229.643 233.89c-26.054 3.28-52.107 24.846-60.636 49.688-5.683 15.47-107.536 454.219-108.005 462.19-0.949 22.968 12.314 39.844 34.104 44.064 8.055 1.874 266.703 1.874 275.232 0 24.631-4.69 47.843-23.908 56.372-47.345 3.316-8.906 108.48-456.093 108.48-463.595 1.422-21.096-11.372-38.44-31.738-44.533-4.739-0.934-262.912-1.874-273.81-0.47z" fill="#E60012" p-id="5246"></path><path d="M470.762 233.89c-26.054 3.28-52.107 24.846-60.632 49.688-5.214 15.47-107.534 454.219-107.534 462.19-0.95 22.968 12.314 39.844 34.108 44.064 8.05 1.874 266.698 1.874 275.227 0 24.635-4.69 47.847-23.908 56.372-47.345 3.317-8.906 108.479-456.093 108.479-463.595 1.424-21.096-11.366-38.44-31.736-44.533-5.214-0.934-263.387-1.874-274.284-0.47z" fill="#00508E" p-id="5247"></path><path d="M722.308 233.89c-26.054 3.28-52.112 24.846-60.637 49.688-5.209 15.47-107.534 454.219-107.534 462.19-0.945 22.968 12.32 39.844 34.108 44.064 8.055 1.874 200.383 1.874 208.908 0 24.634-4.69 47.847-23.908 56.372-47.345 3.316-8.906 108.484-456.093 108.484-463.595 1.418-21.096-11.371-38.44-31.743-44.533-4.734-1.404-220.748-2.343-231.645-0.934h23.687v0.465z" fill="#00908C" p-id="5248"></path><path d="M221.589 376.39c0 0.47-0.476 3.282-1.42 6.095-10.896 36.562-21.793 85.781-20.37 91.874 3.79 18.283 30.793 16.876 40.266-2.343 2.842-5.154 23.212-90.936 23.212-97.028 0-0.47 32.212 0 32.212 0.464 0 0 0 1.408-0.475 2.817-0.474 1.403-5.683 21.091-11.366 44.529-12.795 49.688-14.213 54.842-20.845 64.686-21.793 31.878-94.746 30.94-100.429-1.404-1.422-7.032 14.213-88.124 21.32-110.159 0-1.403 37.895-0.469 37.895 0.47z m484.139 0c21.79 4.69 28.42 22.03 18.472 47.816-9.947 25.78-31.262 37.028-68.686 37.028-11.371 0-10.423-1.874-17.529 33.282-1.419 6.562-2.841 12.656-2.841 13.594-0.476 2.343-34.11 2.343-33.635 0 28.426-120.468 30.793-130.78 30.793-131.72l0.474-1.402h34.104c27.003 0.464 34.583 0.464 38.848 1.403z m-292.282 46.408c4.735 0.938 8.05 4.69 8.05 7.97 0 11.717-24.16 19.688-33.16 10.779-8.525-8.905 9.475-22.5 25.11-18.75z m-83.849 8.904c0 0.94-0.474 2.817-0.474 4.22l-0.474 1.878 5.683-2.816c15.16-7.497 29.844-6.094 34.583 3.281 2.841 5.629 2.367 8.91-5.21 43.595-1.422 6.094-3.315 14.534-3.79 18.748-1.897 9.38-0.474 8.91-17.528 8.91-14.687 0-14.687 0-14.212-1.408 0-0.938 1.896-8.435 3.79-17.345 7.58-33.277 8.055-37.967 1.422-37.967-3.79 0-9.004 2.343-9.473 3.75-0.948 3.282-9.478 44.06-9.952 47.812l-0.945 4.22-14.687 0.938c-17.998 0.47-16.58 1.873-12.79-14.064 5.21-20.626 8.055-35.154 9.949-48.28 2.367-14.063 0.948-12.655 14.212-14.532 6.158-0.94 12.315-1.874 14.213-2.343 4.735-1.409 5.683-0.94 5.683 1.403z m225.014-0.464c0 0.933-0.474 2.811-0.474 4.216l-0.476 2.346 5.688-2.816c29.37-14.998 40.737-2.813 32.212 35.628-1.893 8.436-4.265 20.623-5.683 26.25-0.949 6.094-2.372 11.248-2.842 11.717-1.898 1.874-29.844 1.41-29.375 0 0-0.938 1.898-8.435 3.791-16.875 8.056-34.216 8.056-38.436 0.949-38.436-5.683 0-8.525 1.873-9.473 6.092-1.424 5.155-8.53 38.906-9.475 45l-0.948 5.158-14.687 0.47c-17.999 0.465-16.58 2.342-12.316-15.003 4.74-18.75 8.056-36.094 10.423-48.749 1.893-12.187 0.474-10.782 12.315-12.656 5.213-0.938 11.846-1.878 14.214-2.342 4.738-2.348 6.157-1.878 6.157 0z m287.547-0.47c1.892 28.592 2.368 37.028 2.368 37.498 0 0.47 4.264-7.032 8.999-16.406 9.473-18.749 7.58-16.876 18.002-18.28 2.842-0.469 8.525-1.409 12.79-2.342 10.423-1.878 10.423-2.817-1.423 17.81-16.105 27.658-38.368 66.564-46.423 80.627-24.157 43.591-24.157 43.591-44.527 44.06l-12.316 0.47 0.945-3.282c0.474-1.873 1.897-5.623 2.37-8.435l1.42-5.159h3.79c4.265 0 5.209-0.94 9-7.502 1.423-2.342 3.79-6.093 4.738-8.435 1.42-2.343 6.158-9.844 9.949-17.345l7.58-13.125-1.897-17.34c-2.367-20.158-5.209-44.065-6.631-51.097-0.95-6.562-0.95-6.562 7.58-7.5 3.79-0.466 9.948-1.874 13.264-2.343 8.999-2.812 9.947-2.812 10.422-1.874z m-357.183 0.47c36.476 6.562 23.686 69.37-16.106 78.28-27.003 6.094-45.475-4.22-45.475-24.847 0.47-36.093 27.472-59.53 61.58-53.433z m272.86 1.873c1.893 0.938 4.739 2.812 6.158 4.22 2.367 2.342 2.367 2.342 2.367 0.934 0.475-1.873 0-1.873 18.951-4.685 15.158-2.342 14.684-2.342 13.739 1.874-6.158 26.249-11.371 49.217-13.739 60.47-3.315 16.876-0.948 14.998-19.421 14.529h-15.635v-1.874c0-1.873-0.945-2.812-1.894-1.407-5.213 8.44-30.792 5.158-37.898-5.155-17.525-26.25 19.896-81.562 47.371-68.906z m-340.129 13.595s0 2.342-0.474 4.219c-3.786 14.999-10.418 45.469-11.842 51.092l-1.422 7.032-15.632 0.469c-18.472 0.47-17.528 1.404-13.738-9.843 3.316-10.783 6.633-23.908 8.53-37.972 1.892-12.186 0-10.313 13.738-12.186 6.157-0.94 12.79-1.873 14.208-2.342 3.79-0.939 6.158-0.939 6.632-0.47z m82.9 97.028c0 0.47-0.944 2.348-2.367 4.69-0.95 2.342-2.367 4.22-2.367 4.22 27.946 0.464 28.895 0.464 28.42 1.873l-5.209 16.876h-40.74l-2.367 1.873c-5.214 4.689-32.686 10.782-32.686 7.031l5.21-16.875h3.789c6.632 0 8.05-1.404 13.738-11.247l4.735-8.91c24.636-0.465 29.843 0 29.843 0.47z m62.06 0c0 0.47-0.475 2.812-1.424 5.629-0.948 2.342-1.417 4.685-1.417 5.154 0 0 2.366-0.94 5.207-3.28 10.423-7.033 19.422-8.437 45.95-8.437 10.423 0 19.422 0 19.896 0.465 0.475 0.939-15.156 51.565-17.528 56.25-3.316 6.098-6.633 9.379-11.84 11.721l-4.74 1.874-26.998 0.47-27.003 0.468-4.738 15.937c-9.474 30.47-9.474 28.128 4.264 26.72 10.897-0.94 10.423-1.873 7.107 9.374l-2.842 9.375h-13.738c-29.844 0.47-33.634-1.404-30.793-13.594 1.423-6.094 35.528-117.656 36.002-118.595 0.474-0.465 24.635-0.465 24.635 0.47z m124.584 0c0 0.47-0.474 1.877-0.944 3.75-1.423 4.69-1.423 4.69 4.735 1.41 8.054-4.221 27.002-5.629 65.845-5.629h12.32v5.629c0 6.562 0.475 7.03 6.158 7.966l4.264 0.469-2.372 8.44-2.366 8.435h-8.53c-21.789 0.47-25.104-1.873-25.58-14.528v-6.098l-1.418 4.22-1.423 4.69H733.2c-2.367 0-4.735 0-4.735 0.47 0 0-23.211 76.401-26.528 87.184-0.474 0.94 0 1.409 2.843 1.409 4.264 0 4.264 0 2.841 3.745-1.419 4.221-1.419 4.221 3.316 4.221 3.316 0 5.214-0.47 7.58-1.873 3.317-1.878 3.317-1.408 18.473-22.5l6.158-8.909h-12.79c-15.631 0-14.207 0.939-11.366-8.435l2.367-7.502h31.268c2.841-9.844 3.785-12.656 3.785-13.125 0-0.47-6.632-0.47-15.156-0.47h-15.158l4.735-16.875h42.633c23.212 0 42.638 0 42.638 0.47 0 0.469-0.948 4.22-2.37 8.44l-2.368 7.966-14.213 0.469-14.21 0.469c-2.37 7.032-3.315 10.313-3.789 11.252l-0.474 1.873h13.738c16.106 0 15.157-0.938 11.84 8.436l-2.367 7.501h-31.266l-4.735 5.624h12.316l1.892 11.252c1.898 12.656 1.898 12.656 8.055 12.656 4.74 0 4.74-0.94 1.424 10.312l-2.847 9.375h-8.999c-15.631 0-18.473-2.342-21.32-18.28l-1.418-10.313-5.683 7.502c-15.636 21.091-16.58 21.56-36.476 21.56-12.794 0-12.794 0-10.897-3.75 0.475-1.873 0.475-1.873-3.316-1.873-3.79 0-3.79 0-4.738 2.812l-0.475 2.811H666.88l0.474-1.408c1.424-4.684 3.79-4.215-25.105-4.215-25.109 0-26.527 0-26.053-1.409l2.368-8.435c2.841-8.44 2.367-8.44 5.209-8.44 2.37 0 2.37 0 3.315-3.281 22.268-73.592 29.374-97.03 30.319-100.31l1.897-6.094h13.264c8.525 0 14.682 0.465 14.682 0.934z m-168.167 40.314l-5.213 16.406h-28.42c-2.842 9.375-3.79 12.187-4.266 13.125-0.474 1.409 0.475 1.409 14.214 1.409 8.054 0 14.686 0.47 14.686 0.47 0 0.464-0.474 1.402-0.95 2.81-0.473 0.935-1.422 4.686-2.365 8.436l-1.898 6.093H475.5l-3.316 11.252c-4.738 15.937-4.264 16.406 13.738 14.064 7.581-0.939 7.107-1.877 3.79 9.375l-2.841 9.374h-19.9c-31.263 0-31.737-0.939-23.212-27.658 2.372-8.436 4.74-15.468 4.74-15.468s-3.317-0.47-8.056-0.47c-4.26 0-8.05 0-8.05-0.469 3.315-11.716 4.734-15.468 4.734-16.406 0.474-0.934 1.423-1.403 8.53-1.403h8.05l3.79-14.534h-7.576c-5.688 0-7.58 0-7.58-0.939 0-0.933 4.264-14.528 4.733-15.937 0.95-0.933 72.483-0.469 72.008 0.47z m91.9 33.752c0 0.938-0.949 3.75-1.42 6.093-2.846 13.125-6.631 15.937-21.792 16.875-5.209 0.47-9.948 0.938-9.948 0.938-0.949 1.405-0.949 12.657 0 14.53l1.423 1.877 9.473-0.47c5.21-0.469 9.474-0.469 9.474-0.469 0 0.939-5.21 17.345-5.684 17.81-0.948 0.94-29.843 0.47-33.634-0.934-5.683-1.878-5.683-1.409-5.214-30.94l0.476-25.78h24.16v9.844h4.74c5.208 0 5.682-0.47 8.05-7.033l1.422-3.75h9.475c8.524 0.47 9.473 0.47 8.999 1.409z m44.53-200.626l-5.213 22.5h7.107c36.476 0.938 51.637-40.784 16.105-43.596-5.684-0.47-10.893-0.939-11.367-0.939-1.422 0-1.422 1.41-6.631 22.035z m-188.542 31.874c-8.999 3.75-17.524 37.501-10.417 42.656 5.208 4.22 12.79-2.812 16.58-14.529 6.158-21.565 4.26-31.878-6.163-28.127z m282.339 2.342c-9.478 4.69-16.58 37.971-9.478 41.721 9.478 5.155 21.794-12.654 21.794-31.877 0-8.905-5.214-13.125-12.316-9.844zM557.927 562.957l-2.368 8.436c-1.422 4.22-2.367 7.97-2.841 8.435 0 0.47 2.367-0.465 5.683-2.343 9.473-4.685 12.316-5.623 26.053-6.092l11.846-0.47c1.893-5.623 2.368-7.966 2.368-8.435 0.474-0.47-40.267-0.47-40.741 0.47z m-8.05 27.185l-1.899 7.5 40.741-0.47 2.368-7.5c-32.212 0-41.21 0-41.21 0.47z m128.848-16.876c-1.896 6.097-3.315 10.782-2.841 10.782l5.683-1.873c2.842-0.94 8.055-2.347 10.897-2.812 2.841-0.469 5.209-0.94 5.683-0.94 0 0 4.74-14.532 4.74-15.001 0 0-4.74-0.465-10.423-0.465H682.04l-3.316 10.31z m-7.58 23.907c0 0.469-1.893 5.159-3.317 10.782-1.892 5.624-3.315 10.783-3.315 10.783 0 0.465 2.367-0.47 5.683-1.878 3.316-1.403 8.055-2.812 10.897-3.28 6.157-0.94 6.631-1.404 7.107-3.751 0.474-0.935 1.422-4.685 2.366-7.497l1.898-5.628H682.04c-5.683 0-10.897 0-10.897 0.469z m-16.58 53.908l20.845 0.47c4.264-14.064 5.683-18.754 5.683-19.223l-20.844-0.934-5.684 19.687z" fill="#FFFFFF" p-id="5249"></path></svg><span style="margin-left: 3px;">银行卡</span>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 10px 0; align-items: center; border-bottom: 1px solid #eee;">
                            <div style="width: 91px; flex-shrink: 0;">收款姓名</div>
                            <div style="display: flex; width: 100%;">

                                <input style="width: 100%; border: 0; outline: none; padding: 10px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" value="<%=pmlist["payment_name"] %>">
                            </div>
                        </div>
                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 10px 0; align-items: center; border-bottom: 1px solid #eee;">
                            <div style="width: 91px; flex-shrink: 0;">收款卡号</div>
                            <div style="display: flex; width: 100%;">

                                <input style="width: 100%; border: 0; outline: none; padding: 10px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" value="<%=pmlist["payment_bankid"] %>">
                            </div>
                        </div>
                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 10px 0; align-items: center; border-bottom: 1px solid #eee;">
                            <div style="width: 91px; flex-shrink: 0;">银行类型</div>
                            <div style="display: flex; width: 100%;">

                                <input style="width: 100%; border: 0; outline: none; padding: 10px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" value="<%=pmlist["payment_bankname"] %>">
                            </div>
                        </div>
                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 10px 0; align-items: center; border-bottom: 1px solid #eee;">
                            <div style="width: 91px; flex-shrink: 0;">开户行地址</div>
                            <div style="display: flex; width: 100%;">

                                <input style="width: 100%; border: 0; outline: none; padding: 10px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" value="<%=pmlist["payment_bankbranch"] %>">
                            </div>
                        </div>
                    </div>

                    <div style="background: #F5F5F5; padding: 15px;">
                        <div style="color: #5EBFC6; display: flex; align-items: center;">
                            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9569" width="18" height="18">
                                <path d="M512 64a448 448 0 1 0 448 448 448 448 0 0 0-448-448z m3.2 704a48 48 0 0 1-48-47.36 48.64 48.64 0 0 1 48-48 48 48 0 0 1 47.36 48 47.36 47.36 0 0 1-47.36 47.36z m80.64-271.36A131.84 131.84 0 0 0 547.84 576v32a33.28 33.28 0 0 1-64 0v-24.32a181.76 181.76 0 0 1 67.2-120.32 165.76 165.76 0 0 0 60.16-104.96 83.2 83.2 0 0 0-90.24-89.6c-64 0-99.84 41.6-101.76 119.68a32 32 0 0 1-31.36 28.8 31.36 31.36 0 0 1-30.72-28.8V384a154.24 154.24 0 0 1 163.84-166.4 135.04 135.04 0 0 1 147.2 140.8 196.48 196.48 0 0 1-72.32 138.24z" p-id="9570" fill="#5EBFC6"></path></svg>&nbsp;挂单教程
                        </div>

                        <p style="color: red; font-weight: bold; font-size: 15px;">① 可同时挂单<%=pmlist["order_number"] %>次</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">② 选择挂单金额,然后支付【等待回款即可】</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">③ 释放双手,无需抢单,自动抢单！</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">④ 挂单时间 9点 - 23点【挂单期间请保持与助理联系】</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">⑤ 挂单成功,请注意收款账号余额变动,到账第一时间确认,超时不确认系统会强制确认,并禁止您使用挂单功能!</p>

                        <a style="display: inline-block; background: #72C4CC; color: #fff; width: 100%; padding: 12px; box-sizing: border-box; text-align: center; border-radius: 3px;" onclick="receive_order('',1)">我已了解，确认挂单</a>
                    </div>



                    <div style="border-radius: 4px; border: 1px solid #24b7c5; background-color: #eef8f9; display: flex; align-items: center; justify-content: center; color: #24b7c5; width: 99px; margin-bottom: 10px; margin-top: 18px; padding: 4px 0; font-weight: bold; font-size: 13px;">
                        佣金说明
                    </div>



                </div>

                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>
        </div>


        <script>

            $('#orderTop_menu>div').on('click', function () {
                $(this).addClass("active").siblings().removeClass("active");
                var page = $(this).data("page");
                switch (page) {
                    case "index":
                        $('.orderlist_page').hide();
                        $('.getOrder_page').show();
                        break;
                    case "list":
                        show_orderList('task', 0);
                        $('.getOrder_page').hide();
                        $('.orderlist_page').show();
                        break;
                    default:
                        break;

                }
                console.log('page', page);
            });

            setInterval(function () {
                if ($('#orderTop_menu>div.active').data("page") == "list") {
                    console.log('list刷新');
                    show_orderList('task', 0);
                }
            }, 10000);
        </script>

    </div>


    <div class="pg-gd-history order_page" style="display: none;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="display: flex; background: #73C3CB; color: #fff; padding: 12px 0; font-weight: bold;">
                    <div>
                        <a class="ppreturn" style="height: 100%; width: 50px; display: flex; align-items: baseline; justify-content: center;" onclick="return_ontouch()">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#fff" p-id="27858"></path></svg></a>
                    </div>

                    <div style="width: 100%; text-align: center; position: relative; left: -18px;">进行中的挂单</div>

                    <%--<div style="width: 102px;">
                        <a onclick="open_gdlist()">挂单记录</a>
                    </div>--%>
                </div>


                <div class="guadan_list" style="background: rgb(231, 231, 231); color: rgb(202, 211, 199); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; margin-top: 10px;">

                    <div class="user_guadan" style="border-radius: 8px; text-align: center; color: rgb(154, 155, 152); font-weight: bold; font-size: 14px; margin: 10px;">
                    </div>


                </div>


                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>

        </div>

    </div>


    <div id="task_page" class="order_page" style="display: none;">
        <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">
            <div>
                <b>收款方式</b>

                <div style="margin-top: 18px; display: flex; align-items: center;" onclick="switch_payment();" id="paymentid">
                    <div style="color: gray; text-align: center; font-size: 12px; padding: 12px; width: 100%;">请选择收款账户</div>
                </div>


            </div>
        </div>

    </div>



    <div id="untask_page" class="order_page" style="display: ;">


        <div style="background: #fff; padding: 12px 25px; border-radius: 5px; margin: 10px 0; margin-bottom: 0; padding-bottom: 0;">

            <h2 style="margin: 0; margin-bottom: 10px;">今日战果
                <div style="display: inline-block; font-size: 15px; color: #5a5b5c;">
                    <%if (pmlist["show_type"] + "" == "amount")
                        {
                    %>【今日剩余搬砖额度 <span id="shuadan_number"><%=positive_number((Convert.ToDouble(pmlist["amount"])+Convert.ToDouble(uConfig.gd(userdt,"share_reward_amount"))).ToString("0")) %></span>元】<%
                                                                                         }
                                                                                         else
                                                                                         {
                    %>【今日剩余搬砖次数 <span id="shuadan_number"><%=positive_number(Convert.ToDouble(pmlist["number"])+0) %></span>次】<%
                } %>
                </div>
            </h2>



            <div style="margin-top: 16px;">

                <div style="display: flex;">

                    <div style="flex-shrink: 0; margin-right: 2px;">

                        <h2 style="margin: 0; color: #fe6225;"><span id="total_amount">0</span>
                        </h2>
                        <div style="margin-bottom: 5px;"><span style="font-size: 12px; color: #aaa; font-weight: 500;">(冻结 0.00)</span></div>
                        <div style="font-size: 12px;">
                            今日搬砖总额
                        </div>

                    </div>

                    <div style="display: flex; width: 100%; font-size: 12px;">
                        <div style="width: 50%; margin-bottom: 30px; margin: auto; display: flex;">

                            <div style="text-align: center; margin-left: auto;">
                                <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="total_award_number">0</div>

                                <div style="color: #31394a;">
                                    历史次数
                                </div>
                            </div>
                        </div>

                        <div style="width: 50%; margin-bottom: 30px; margin: auto; display: flex;">

                            <div style="text-align: center; margin-left: auto;">
                                <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="total_award_amount">0.00</div>

                                <div style="color: #31394a;">
                                    历史佣金
                                </div>
                            </div>
                        </div>
                    </div>


                </div>


                <div style="display: flex; font-size: 12px; margin-top: 20px; flex-wrap: wrap;">

                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            今日搬砖佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="today_amount">0.00</div>

                    </div>

                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            昨日搬砖佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="yesterday_amount">0.00</div>

                    </div>

                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            今日一键搬砖佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="today_onetouch_amount">0.00</div>

                    </div>



                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            今日搬砖数
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="today_number">0</div>

                    </div>
                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            昨日搬砖数
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="yesterday_number">0</div>

                    </div>

                    <div style="width: 33.333333%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            历史一键搬砖佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="total_onetouch_amount">0.00</div>

                    </div>


                </div>


            </div>
        </div>


        <div>
        </div>

        <style>
            .tt-table {
                font-weight: bold;
                border-collapse: collapse;
            }

                .tt-table th {
                    text-align: center;
                    color: #fff;
                    border: solid 1px #999;
                    /* background: #0082f3; */
                    font-size: 18px;
                    font-weight: 500;
                    padding: 5px 0;
                    /* color: #000; */
                    border: solid 1px #ffd1bf;
                    background: #DF5054;
                    color: #fff;
                    text-shadow: 2px 3px 2px #9d978a;
                }

                .tt-table.table-gd th {
                    border: solid 1px #72c4cc;
                    background: #24b7c5;
                }

                .tt-table td {
                    text-align: center;
                    border: solid 1px #999;
                    font-size: 16px;
                    line-height: 22px;
                    padding: 5px 0;
                    border: solid 1px #FE6225;
                    color: #2a2b2c;
                    text-shadow: 2px 3px 2px #5a5b5c40;
                    /*background: #FFE4E1;*/
                }


                .tt-table.table-gd td {
                    border: solid 1px #72c4cc;
                }

                .tt-table tr.active {
                    background: #e1aeae36;
                }

                .tt-table.table-gd tr.active {
                    background: #aedce136;
                }
        </style>


        <table width="100%" class="tt-table" style="">
            <tbody id="levels">
                <tr class="firstRow">
                    <th>矿工名称</th>
                    <th>搬砖金额</th>
                    <th>佣金比例</th>
                    <%if (uConfig.stcdata("task_funcs").IndexOf("fzyj") != -1)
                        {
                    %>
                    <th>房主佣金</th>
                    <%} %>
                </tr>
                <asp:Repeater ID="levels_list" runat="server">
                    <ItemTemplate>
                        <tr leftamount="<%#Eval("leftAmount") %>" rightamount="<%#Eval("rightAmount") %>" rate_award="<%#Eval("rate_award") %>">
                            <td><%#Eval("name") %></td>
                            <td><%#Eval("leftAmount") %>-<%#Eval("rightAmount") %></td>
                            <td><%#Eval("rate_award") %>%</td>
                            <%if (uConfig.stcdata("task_funcs").IndexOf("fzyj") != -1)
                                {
                            %>
                            <td style="color: #dd4848;"><%#Eval("serve_fee") %>%</td>
                            <%} %>
                        </tr>
                    </ItemTemplate>
                </asp:Repeater>
            </tbody>
        </table>

        <script>
            var get_rateaward = function (amount) {
                var rate_award = 0;
                $('#levels tr[leftAmount]').each(function () {
                    var leftAmount = $(this).attr('leftAmount');
                    var rightAmount = $(this).attr('rightAmount');
                    var data = $(this).attr('rate_award');

                    if (amount >= parseNumber(leftAmount) && amount <= parseNumber(rightAmount)) {
                        rate_award = parseNumber(data);
                    }

                    //console.log('data', leftAmount, rightAmount, data, amount + "→【" + rate_award + "】");


                })
                return rate_award;
            }
        </script>

    </div>



    <div id="newtip" style="display: none;">

        <div style="display: block; margin-bottom: 10px;">
            <div style="flex-shrink: 0; display: flex; flex-direction: column; justify-content: center; line-height: 21px; /* font-weight: bold; */color: #1CAAFD; background: #f0f0f0; font-size: 12px; padding: 10px;">

                <div style="font-size: 18px; margin-bottom: 10px;">
                    温馨提示：
                </div>

                <div>1.保持在线状态才会派订单；</div>
                <div>2.收到款后请及时点击确认收款，恶意不确认可能会被禁止接单；</div>
                <div>3.有新订单会语音提醒，部分手机要保持屏幕亮才有语音提醒</div>


            </div>
        </div>

    </div>



    <div id="list-container" style="background: rgb(231, 231, 231); border-radius: 8px; padding: 38px; text-align: center; color: rgb(154, 155, 152); font-weight: bold; font-size: 14px; display: none;">

        <div id="order_lists" class="user_orderList">
        </div>
    </div>

    <script>
        var pdt = {
            '银行卡': {
                icon: '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18868" width="32" height="32"> <path d="M534.493867 78.762667l359.850666 346.9312c26.112 25.088 26.9312 67.754667 1.8432 93.866666L697.105067 724.8896c-25.088 26.094933-67.754667 26.9312-93.8496 1.8432L243.4048 379.818667c-26.094933-25.088-26.9312-67.754667-1.8432-93.8496L440.644267 80.622933c25.088-26.112 66.696533-28.296533 93.8496-1.860266z" fill="#FFD3D7" p-id="18869"></path><path d="M322.935467 155.989333l460.288 195.396267c33.006933 13.994667 49.937067 52.718933 35.2768 87.278933l-111.36 262.3488c-14.011733 32.989867-52.736 49.937067-87.278934 35.259734L159.556267 540.893867c-32.989867-13.994667-49.937067-52.718933-35.259734-87.278934l111.36-262.3488c13.994667-33.006933 54.272-49.271467 87.278934-35.2768z" fill="#FB560A" p-id="18870"></path><path d="M150.186667 708.266667h723.626666l92.16 128c34.133333 47.786667 15.36 85.333333-44.373333 85.333333H93.866667C35.84 921.6 17.066667 884.053333 51.2 836.266667l98.986667-128z" fill="#DAE9FF" p-id="18871"></path><path d="M225.28 349.866667h597.333333c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52h-597.333333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#DAE9FF" p-id="18872"></path><path d="M208.213333 349.866667h558.08c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52H208.213333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#3889FF" p-id="18873"></path><path d="M605.866667 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18874"></path><path d="M691.2 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18875"></path><path d="M776.533333 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18876"></path><path d="M233.813333 460.8c-15.36 0-29.013333 11.946667-29.013333 27.306667 0 15.36 11.946667 27.306667 29.013333 27.306666h527.36c15.36 0 29.013333-11.946667 29.013334-27.306666 0-15.36-11.946667-27.306667-29.013334-27.306667H233.813333z" fill="#DAE9FF" p-id="18877"></path><path d="M216.746667 443.733333c-15.36 0-29.013333 11.946667-29.013334 27.306667 0 15.36 11.946667 27.306667 29.013334 27.306667h527.36c15.36 0 29.013333-15.36 29.013333-27.306667s-13.653333-27.306667-29.013333-27.306667H216.746667z" fill="#FFFFFF" p-id="18878"></path></svg>'
            }
            , '支付宝': {
                icon: '<img src="../static/images/pay/支付宝.png" width="32" height="32">'
            }
            , '支付宝二维码': {
                icon: ' <img src="../static/images/pay/支付宝二维码.png" width="32" height="32">'
            }
            , '微信二维码': {
                icon: '<img src="../static/images/pay/微信二维码.png" width="32" height="32">'
            }
        }

        var show_orderList = function (state, order_mode) {
            v3api("lists", { data: { page: 'transport_orders', p: 0, limit: 999, state: state, order_mode: order_mode } }, function (e) {


                var tableId = '.user_orderList';
                if (order_mode == 1) {
                    tableId = '.user_guadan';
                }


                $(tableId).html("");


                $('#task_order_number').html(e.data.list.length);
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];


                    var __icon = '';
                    try {
                        __icon = pdt[obj.payment_type].icon;
                    } catch (e) {

                    }
                    if (__icon == "") {
                        __icon = '<span style="color:blue;">【请绑定回款类型】</span>';
                        __click = 'onclick="tp(\'请绑定回款类型\')"';
                    }


                    var buttons = '';
                    if (obj.state == 1) {
                        buttons = '<a style="border-radius: 18px;padding: 12px 26px;background: #ff0008;color: #fff;cursor: pointer;border: 2px solid #c72d2e;margin-left: auto;font-weight: bold;font-size: 30px;display: inline-block;height: 60px;line-height: 60px;width: 139px;" onclick="set_orderstate(\'' + obj.sd_orderNo + '\',1)">                            确认收款</a> ';
                    }

                    $(tableId).append(' <div listid="' + obj.sd_orderNo + '" style="background: #fff; border-radius: 5px; padding: 5px 9px; margin: 10px 0; border-bottom: 2px solid #ddd; font-weight: 500;">                <div style="background: #f8f8f8; padding: 10px 16px; border-radius: 8px; position: relative;">                    <img src="/static/images/order_tasking.png" style="position: absolute; right: 0; top: -8px; width: 80px;    transform: rotate(60deg);">                    <div style="color: #5a5b5c; font-size: 12px; text-align: -webkit-auto; font-weight: 100;">                        <div>搬砖时间：' + obj.receive_time + '</div>                        <div style="">搬砖编号：' + obj.sd_orderNo + '</div>                    </div>                                 <div style="display: flex; margin: 10px 0;">                            <div>订单总额                                                          </div>                            <div style="margin-left: auto; color: #222;">                                <span class="total_amount">' + parseFloat(obj.orderAmt).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                            </div>                        </div>                        <div style="display: flex; margin: 10px 0;">                            <div>佣金</div>                            <div style="margin-left: auto;color: #222;text-align: right;">                                <span class="award_amount" style="' + (obj.is_double == 1 ? 'color: #ee7d25; font-weight: bold; font-size: 18px;' : '') + '">' + parseFloat(obj.award_amount - (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : parseFloat(obj.serve_fee)) + (obj.is_double == 1 ? obj.award_amount : 0)).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">              <div style="    color: #000;    font-weight: bold;">佣金转入余额' + (obj.is_double == 1 ? '<span style="color: #ee7d25; font-weight: bold; background: #000000e3; font-size: 12px; padding: 2px 10px; border-radius: 5px; margin-left: 6px; box-shadow: 2px 2px 8px #ee7d25;">        <svg t="1701338602094" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9074" width="12" height="12" style="margin-right: 3px;">            <path d="M859.995 460.963c-1.197 10.478-1.197 21.854 0 33.527v88.305c0 160.751-110.161 296.657-258.34 336.17v-52.982c0-49.693-40.112-89.805-89.505-89.805h-0.3c-49.394 0-89.805 40.112-89.805 89.805v52.982c-148.179-39.812-258.04-175.719-258.04-336.17V431.328c39.814 32.33 93.098 91.302 89.805 172.726 33.227-153.267 99.085-176.318 154.764-239.48 72.444-82.321 93.996-172.726 52.386-259.538C641.17 167.6 687.868 409.476 672.901 536.7c64.959-115.251 146.682-146.083 187.094-154.166v78.429z" fill="#FF7B15" p-id="9075"></path></svg>双倍收益</span>' : '') + '</div>              </div>                        </div>                                        </div>         ' +

                        (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : ('<div style="display: flex; margin: 10px 0;">                            <div>房主佣金                                                          </div>                            <div style="margin-left: auto; color: #222;">                                <span class="total_amount">' + parseFloat(obj.serve_fee).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                            </div>                        </div>'))

                        + `<div style="display: flex; margin: 10px 0;">                            <div>当前汇率                                                          </div>                            <div style="margin-left: auto; color: #222;">                                <span class="total_amount">≈${usdt.prices.app}</span>                           </div>                        </div>`


                        + ((obj.other_orderNo == '-' && obj.order_mode != 1 ? '' : '    <div style="color: blue; font-size: 22px;">        <div style="display: flex; margin: 10px 0;">            <div style="text-shadow: 5px 5px 5px #0000ff29;">                商家姓名：            </div>            <div style="margin-left: auto; color: #222;">                <span style="color: #ff2626; font-size: 22px; font-weight: bold; text-shadow: 5px 5px 5px #ff00002b;" class="payerName payerName-1" data-id="' + obj.sd_orderNo + '"  data-mid="' + obj.mid + '">' + obj.payerName + '</span>            </div>        </div>    </div>') + '       <div style="margin-top: 20px; display: flex; justify-content: center;padding-top: 0;">                       <div style="display: flex; justify-content: center;">                        ' +
                            (order_mode == 1 ?
                                '<div style="display: inline-block;border: 5px solid #ddd;border-radius: 50%;width: 118px;height: 118px;display: flex;justify-content: center;align-items: center;background: #eee;" onclick="cancel_guadan(\'' + obj.sd_orderNo + '\')">                            <div style="color: ' + (obj.cancel_apply == 1 ? '#e74443' : '#2a2b2c') + ';font-size: 18px;font-weight: bold;">                                ' + (obj.cancel_apply == 1 ? '取消中' : '取消挂单') + '</div>                        </div>'

                                :

                                '<div style="display: inline-block; border: 1px solid blue; border-radius: 50%; width: 130px; height: 130px; display: flex; justify-content: center; align-items: center;" class="countdown-1" data-duration="' + obj.duration + '">                            ' + (obj.orderLock == 1 ? (obj.state == 1 ? '<div style="color: blue; font-size: 20px; font-weight: bold;"><div>商家已回款</div><div style="margin-top: 10px">请及时确认</div></div>' : '<div style="color: blue; font-size: 20px; font-weight: bold;"><div>商家已确认</div><div style="margin-top: 10px">请等待回款</div></div>') : '<div style="color: blue; font-size: 20px; font-weight: bold;">                                                                等待商家响应                                               </div>') + '                        </div> ')


                            + '                   </div>                              <div style="display: flex;align-items: center;margin-left: 8px;flex-direction: column;"><div style="    color: #4040f3;    font-size: 16px;    margin-bottom: 10px;    font-weight: bold;">商家回款金额：' + cny((parseFloat(obj.orderAmt) - (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : parseFloat(obj.serve_fee))).toFixed(2)) + 'CY</div>   ' + buttons + '     </div>       </div>               ' + (obj.other_orderNo == '-' ? '' : '<div style="border-top: 1px solid #ddd; margin-top: 8px; padding-top: 8px; color: red; font-weight: bold; font-size: 18px;">        温馨提示：收到的款项，汇款人与商家姓名<span style="color: blue;">（<span class="payerName">' + obj.payerName + '</span>）</span>不一致！请联系密信专员    </div>')) + '        </div>            </div>');
                }

                $('#list-container').css({ "padding": "8px" });
                $('#list-container').css({ "padding-bottom": "18px" });

                if (e.data.list.length == 0) {
                    $(tableId).html('<div>            <svg t="1692619408833" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32388" width="64" height="64"><path d="M234.656 213.344H256A106.656 106.656 0 0 0 362.656 320h298.688A106.656 106.656 0 0 0 768 213.344h21.344a85.344 85.344 0 0 1 85.312 85.312v512A85.344 85.344 0 0 1 789.344 896H234.656a85.344 85.344 0 0 1-85.312-85.344v-512a85.344 85.344 0 0 1 85.312-85.312z m85.344 192A21.344 21.344 0 1 0 320 448h362.656a21.344 21.344 0 1 0 0-42.656H320z m0 149.312a21.344 21.344 0 0 0 0 42.688h362.656a21.344 21.344 0 0 0 0-42.688H320zM320 704a21.344 21.344 0 1 0 0 42.656h234.656a21.344 21.344 0 1 0 0-42.656H320z m42.656-554.656h298.688a64 64 0 1 1 0 128h-298.688a64 64 0 0 1 0-128z" fill="#9A9B98" p-id="32389"></path></svg>        </div>        <div style="    margin-top: 8px;">            暂无进行中的订单        </div>');
                    $('#list-container').css({ "padding-bottom": "0px" });
                    $('#list-container').css({ "padding": "38px" });
                }

                startCountdown();

            })
        };

        show_orderList('task', 0);


        var updatePayerName = function () {

            //if ($('[data-page="list"]').hasClass('active') == false) {
            //    return;
            //}

            var ids = [];
            $('.payerName-1').each(function () {
                var tt = $(this).text();
                var id = $(this).data("id");
                var mid = $(this).data("mid");
                if (tt == "" || tt == "获取中") {
                    $('[listid="' + id + '"]').find(".payerName").html('获取中');
                    ids.push(mid);
                }
            });

            if (ids.length == 0) {
                return;
            }

            v3api("getPayerName", { error: 1, data: { ids: ids.join(',') } }, function (e) {
                if (e.code == 1) {
                    for (var i = 0; i < e.success.length; i++) {
                        $('[listid="' + e.success[i].id + '"]').find(".payerName").html(e.success[i].name);
                    }
                }
            })
        }

        setInterval(function () {
            updatePayerName()
        }, 2000);


        var confirm_transaction = function (id) {
            event.stopPropagation();
            v3api("confirm_order", { data: { id: id } }, function (e) {
                tp(e.msg);
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }
        var get_userLimitState = function () {

            v3api("get_userLimitState", {
                error: 1,
                data: {}
            }, function (e) {
                if (e.code == 1) {
                    $('#u_current_amount').html(e.value);
                }
            })

        }

        var set_orderstate = function (id, state) {
            event.stopPropagation();



            //security_password(function (e) {


            //    v3api("set_orderstate", {
            //        data: {
            //            paypwd: e.password,
            //            id: id,
            //            state: state
            //        }
            //    }, function (e) {
            //        tp(e.msg);
            //        var ls = $('[listid=' + id + ']');
            //        ls.remove();
            //        $('#task_order_number').html(parseInt($('#task_order_number').html()) - 1);

            //        update_userAmount(e);

            //        get_userLimitState();
            //    })



            //}, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">确定收到款了吗？</div><div>为了保证您的资金安全，<span style="color:red;">请收到款再确认收货</span></div>    </div>' })


            v3api("set_orderstate", {
                data: {
                    //paypwd: e.password,
                    id: id,
                    state: state
                }
            }, function (e) {
                tp(e.msg);
                var ls = $('[listid=' + id + ']');
                ls.remove();
                $('#task_order_number').html(parseInt($('#task_order_number').html()) - 1);

                update_userAmount(e);

                get_userLimitState();
            })


        }
    </script>




    <div class="a order_page" style="background: linear-gradient(209deg, #ffcc2c, #ff9a2c); border-radius: 0.106667rem; display: inline-block; text-align: center; color: #fff; padding: 5px 30px; font-size: 18px;font-weight:bold; margin-top: 18px;">
        搬砖流程
    </div>

    <div class="order_page" style="background: #fff; border-radius: 8px; padding: 18px; font-size: 15px; margin-top: 10px; color:#2a2b2c; line-height: 22px; padding-top: 0;">
        
  

        
  <div style="color:#2a2b2c;font-weight:600;margin-top:5px;">
<div>第一步：开始搬砖</div>
<div>第二步：选择搬砖金额抢单</div>
<div>第三步：等待商家回款RMB</div>
<div>第四步：收到款项，及时确认！确认后订单完成</div>
  </div>



    </div>





    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>


    <script>



        var payment_icons = {
            bank: '<svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12063" width="32" height="32">                        <path d="M648.8064 150.0928a29.2608 29.2608 0 0 1 39.936 10.7008l164.7616 285.3632H277.9392c-15.0784 0-27.4944 11.4176-29.0816 26.0864l-0.1792 3.1744V855.808c0 8.7808 3.8912 16.6656 10.0352 22.016a29.184 29.184 0 0 1-25.856-14.592l-190.208-329.3952a29.2608 29.2608 0 0 1 10.7264-39.936l595.4304-343.808z" fill="#D7DBEC" p-id="12064"></path><path d="M71.9104 584.4992L718.0032 211.456l43.904 76.032L115.7888 660.48z" fill="#131523" p-id="12065"></path><path d="M965.4784 435.2c16.1536 0 29.2608 13.1072 29.2608 29.2608V844.8c0 16.1536-13.1072 29.2608-29.2608 29.2608H277.9392A29.2608 29.2608 0 0 1 248.6784 844.8V464.4608c0-16.1536 13.1072-29.2608 29.2608-29.2608h687.5392zM702.208 727.7824H336.4608v58.496h365.7216V727.808z m-256-146.304h-109.7216v87.7824h109.7216v-87.7824z" fill="#476CF0" p-id="12066"></path></svg>',
            alipay: '<svg t="1692522651546" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20838" width="32" height="32"><path d="M230.4 576.512c-12.288 9.728-25.088 24.064-28.672 41.984-5.12 24.576-1.024 55.296 22.528 79.872 28.672 29.184 72.704 37.376 91.648 38.912 51.2 3.584 105.984-22.016 147.456-50.688 16.384-11.264 44.032-34.304 70.144-69.632-59.392-30.72-133.632-64.512-212.48-61.44-40.448 1.536-69.632 9.728-90.624 20.992z m752.64 135.68c26.112-61.44 40.96-129.024 40.96-200.192C1024 229.888 794.112 0 512 0S0 229.888 0 512s229.888 512 512 512c170.496 0 321.536-83.968 414.72-211.968-88.064-43.52-232.96-115.712-322.56-159.232-42.496 48.64-105.472 97.28-176.64 118.272-44.544 13.312-84.992 18.432-126.976 9.728-41.984-8.704-72.704-28.16-90.624-47.616-9.216-10.24-19.456-22.528-27.136-37.888 0.512 1.024 1.024 2.048 1.024 3.072 0 0-4.608-7.68-7.68-19.456-1.536-6.144-3.072-11.776-3.584-17.92-0.512-4.096-0.512-8.704 0-12.8-0.512-7.68 0-15.872 1.536-24.064 4.096-20.48 12.8-44.032 35.328-65.536 49.152-48.128 114.688-50.688 148.992-50.176 50.176 0.512 138.24 22.528 211.968 48.64 20.48-43.52 33.792-90.112 41.984-121.344h-307.2v-33.28h157.696v-66.56H272.384V302.08h190.464V235.52c0-9.216 2.048-16.384 16.384-16.384h74.752V302.08h207.36v33.28h-207.36v66.56h165.888s-16.896 92.672-68.608 184.32c115.2 40.96 278.016 104.448 331.776 125.952z" fill="#06B4FD" p-id="20839"></path></svg>',
            wepay: '<svg t="1692522671251" class="icon" viewBox="0 0 1144 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21909" width="32" height="32">                    <path d="M436.314353 632.771765c-68.517647 36.321882-78.667294-20.389647-78.667294-20.389647l-85.835294-190.524236c-33.039059-90.533647 28.581647-40.839529 28.581647-40.839529s52.856471 38.038588 93.003294 61.229176c40.086588 23.190588 85.835294 6.806588 85.835294 6.806589l561.212235-246.362353C936.899765 80.112941 765.891765 0 572.235294 0 256.180706 0 0 213.232941 0 476.310588c0 151.311059 84.811294 285.967059 216.937412 373.248l-23.792941 130.288941s-11.625412 38.038588 28.611764 20.389647c27.437176-12.047059 97.370353-55.115294 138.992941-81.347764 65.445647 21.684706 136.734118 33.731765 211.486118 33.731764 316.024471 0 572.235294-213.232941 572.235294-476.310588 0-76.197647-21.594353-148.178824-59.843764-212.028235-178.808471 102.309647-594.733176 340.118588-648.312471 368.489412z" fill="#43C93E" p-id="21910"></path></svg>'
        }

        var payments = {};
        var maxOrderNumber = '<%=uConfig.stcdata("limit_number") %>';
        var switch_payment = function (initPaymentAccount) {

            if (!initPaymentAccount) {
                openButtomPage($('.payway-select').html(), { miss_close: true });
            }

            v3api("lists", { data: { page: 'payment_list', p: 0, limit: 99 } }, function (e) {
                payments = e.data.list;
                $('#lists').html('');
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="margin-top: 27px; display: flex;    align-items: center;" onclick="set_payway(' + obj.id + ')">                <div style="margin-right: 18px;">                    ' + (obj.type == '银行卡' ? payment_icons.bank : obj.type == '支付宝' ? payment_icons.alipay : obj.type == '微信' ? payment_icons.wepay : '') + '                </div>                <div>                    <b style="margin-bottom: 6px; display: inline-block;font-size:16px;">' + obj.bankid + '</b>                    <div style="color: gray;">' + obj.bankname + '</div>        ' + (maxOrderNumber - obj.current_number > 0 ? '<div style="color: #7e77cf;margin-top: 10px;font-weight: bold;">今日可接单次数：' + (maxOrderNumber - obj.current_number) + '次</div> ' : '<div style="color: red;margin-top: 10px;font-weight: bold;">今日不可接单</div> ') + '       </div>                <div style="margin-left: auto; display: flex; align-items: center;">                    <div>                        <span style="font-weight: bold; font-size: 17px; color: #333;">' + obj.name + '</span>&nbsp;&nbsp;                                       </div>                    <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32319" width="16" height="16">                        <path d="M307.2 972.8a51.2 51.2 0 0 0 36.352-14.848l409.6-409.6a51.2 51.2 0 0 0 0-72.192l-409.6-409.6a51.2 51.2 0 0 0-72.704 72.192l373.76 373.248-373.76 373.248A51.2 51.2 0 0 0 307.2 972.8z" fill="#3E4055" p-id="32320"></path></svg>                </div>            </div>');
                }


                //if (initPaymentAccount) {
                //    if (payments.length > 0) {
                //        set_payway(payments[0].id);
                //    }
                //}

            })
        }



        var set_payway = function (id) {
            for (var i = 0; i < payments.length; i++) {
                var obj = payments[i];
                if (obj.id == id) {
                    $('#paymentid').html('<div style="margin-right: 18px;">                    ' + (obj.type == '银行卡' ? payment_icons.bank : obj.type == '支付宝' ? payment_icons.alipay : obj.type == '微信' ? payment_icons.wepay : '') + '                </div>                <div>                    <b style="margin-bottom: 6px; display: inline-block;">' + obj.bankid + '</b>                    <div style="color: gray;">' + obj.bankname + '</div>                </div>                <div style="margin-left: auto; display: flex; align-items: center;">                    <div>                        <span style="font-weight: bold; font-size: 17px; color: #333;">' + obj.name + '</span>&nbsp;&nbsp;                                       </div>                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32319" width="16" height="16">                        <path d="M307.2 972.8a51.2 51.2 0 0 0 36.352-14.848l409.6-409.6a51.2 51.2 0 0 0 0-72.192l-409.6-409.6a51.2 51.2 0 0 0-72.704 72.192l373.76 373.248-373.76 373.248A51.2 51.2 0 0 0 307.2 972.8z" fill="#3E4055" p-id="32320"></path></svg>                </div>').attr('payment_id', id);
                    break;
                }

            }
            closePopup();


            location.href = "order_new.aspx?task=1&payment_id=" + id;
        }


        switch_payment(true);


        var generate_advert_data = function () {
            var number = parseFloat($('#sell_number').val());
            console.log('number', number, isNaN(number))
            if (isNaN(number)) {
                number = 0;
            }

            $('#sell_total_usdt').html(number + '&nbsp;USDT');
            $('#success_cny').html(parseFloat(cny(number)).toFixed(0) + '&nbsp;CNY');
            $('#reward_usdt').html(get_rewark(number) + '&nbsp;USDT');
        }

        $('#sell_number').on('keyup', function () {
            generate_advert_data();
        })

        generate_advert_data();


        $('.select_tab').on('click', function () {
            $(this).addClass("activity").siblings().removeClass("activity");
        })


        var order_data = {};

        var getOrder_timer = 0;
        var checknew_timer = 0;

        var isfirst = true;

        var get_orderList = function (data) {
            if (isfirst) {
                //loading
                console.log('进入');
                layer.open({
                    type: 2,
                    content: '搬砖订单提取中',
                    time: 99,
                    shadeClose: false
                })
                isfirst = false;
            }


            $('.video-icon-container').hide();
            $('body').css({ "overflow": "hidden" });

            //console.log('进入', data, data == '{}');
            //if (data == {}) {
            //    console.log('进入');
            //    layer.open({
            //        type: 2,
            //        content: '获取订单中',
            //        time: 99,
            //        shadeClose: false
            //    })
            //}

            data.tp = '<%=pmlist["tp"] %>';
            data.tp = encodeURIComponent(data.tp);

            $('.pg-select-items').show();
            v3api("get_orderList", {
                error: 1,
                data: data
            }, function (e) {


                layer.closeAll();

                //console.log('ee', e);
                if (e.code == 1) {
                    $('.pg-select-items').show();
                    $('#order_list tbody').html('');

                    for (var i = 0; i < e.list.length; i++) {
                        var obj = e.list[i];
                        var current_rate_award = get_rateaward(obj.orderAmt);
                        //<img src="' + obj.imgurl + '" style="width: 22px; height: 22px; margin-right: 5px; position: relative; top: 5px;">
                        $('#order_list tbody').append('<tr class="order_items" second="' + obj.cdnum + '">                            <td>   ' + obj.title + '</td>                            <td>' + obj.orderAmt + '<img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;"></td>               <td>' + current_rate_award + '%' + '</td>              <td>                                <a style="background: #C8A681;color: #eee;width: 89px;display: inline-block;padding: 4px;border-radius: 30px;" onclick="receive_order(\'' + obj.orderNo + '\',0)">抢单</a>                            </td>                        </tr>');
                    }
                } else {
                    //if (e.code == 9001) {
                    //    $('.pg-select-items').show();
                    //    $('#task_list').html('<div style="height:100vh;display:flex;align-items: center;justify-content: center;"><h2>' + e.msg + '</h2></div>')
                    //    return;
                    //}
                    if (e.type != "push" && $('[data-page="index"]').hasClass('active')) {
                        if (e.msg != "正在刷新订单~") {
                            tp(e.msg);
                        }
                    }
                }

                checknew_timer = setTimeout(function () {
                    get_orderList({ newid: e.newid, new_time: e.new_time });
                }, 2000);


            })
        }

        var get_touch = function () {
            $('.video-icon-container').hide();
            $(".pg-ontouch").show();
            $('body').css({ "overflow": "hidden" });
        }
        var open_gdlist = function () {
            show_orderList('task', 1);
            $(".pg-ontouch").hide();
            $('.pg-gd-history').show();
        }
        var return_ontouch = function () {
            $(".pg-ontouch").show();
            $('.pg-gd-history').hide();
        }
        var cancel_guadan = function (id) {
            v3api("cancel_apply", {
                data: {
                    id: id
                }
            }, function (e) {
                tp(e.msg);
            })
        }


        var update_userAmount = function (e) {
            if (e.current_amount) {
                $('#u_current_amount').html(e.current_amount);
                $('.user_amount').html(parseFloat(e.user_amount).toFixed(2));
                $('#u_current_freeze_amount').html(parseFloat(e.current_freeze_amount).toFixed(2));
            }
        }

        $('#order_button').on('click', function () {
            switch_payment();
        })

        var receive_order = function (orderNo, order_mode, paypwd) {




            //if (order_mode == 1 && typeof (paypwd) == "undefined") {

            //    security_password(function (e) {

            //        receive_order(orderNo, order_mode, e.password);

            //    }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 16px;color:#000;" class="animated-text"><div style="    margin-bottom: 10px;">您确定挂单了吗？</div><div style=" margin-bottom: 5px; "><span style="color:red;">本次挂单金额为 ' + $('#task_amount').val() + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span></div>  <div><span style="color: red;">请于助理保持联络！</span></div>  </div>' })
            //    return;
            //}


            layer.open({
                type: 2,
                content: (order_mode == 1 ? '挂单中' : '正在抢单'),
                time: 99,
                shadeClose: false
            })

            v3api("receive_order", {
                error: 1,
                data: {
                    task_amount: $('#task_amount').val(),
                    order_mode: order_mode,
                    orderNo: orderNo,
                    payment_id: get_param('payment_id')
                    //, paypwd: paypwd
                }
            }, function (e) {
                if (e.msg != '订单已被其他用户抢了') {
                    layer.closeAll();
                    console.log('ee', e);
                    tp(e.msg);
                } else {
                    setTimeout(function () {
                        layer.closeAll();
                        tp(e.msg);
                    }, 3000)
                }
                if (e.code == 1) {
                    $('[data-page="list"]').click();
                    update_userAmount(e);
                }



                get_userLimitState();

            })


        }


        var classId = "";
        var user_amount = "<%=uConfig.gd(userdt, "amount")  %>";
        var set_classId = function (id, amount) {
            if (parseFloat(user_amount) < parseFloat(amount)) {
                tp('余额不足，请充币！');
                return;
            }

            classId = id;

            $('#order_button').html('<div class="rotating-element"></div>正在抢单中');
            $('#untask_page').hide();
            $('.pg-select-items').fadeOut(100)
            $('#task_page').show();
            $('#order_button').show();


            auto_getOrder();
        }


        var order_delay_time = '<%=uConfig.stcdata("order_delay_time") %>';
        var delay_list = order_delay_time.split('~');
        var delay_start = 5;
        var delay_end = 5;
        if (delay_list.length == 2) {
            delay_start = delay_list[0];
            delay_end = delay_list[1];
        }
        function getRandomInt(min, max) {
            min = Math.ceil(min);
            max = Math.floor(max);
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
        var delay_timer = 0;
        var auto_getOrder = function () {
            gdStart();
            clearTimeout(delay_timer);
            var s = getRandomInt(delay_start, delay_end);
            delay_timer = setTimeout(function () {
                console.log('搬砖', s);
                get_order();
            }, s * 1000);
        }

        var get_order = function () {
            gdStart();
            v3api("new_order", {
                error: 1,
                data: {
                    classId: classId,
                    payment_id: $('#paymentid').attr('payment_id')
                }
            }, function (e) {
                console.log('ee', e);
                if (e.code == 1) {
                    gdEnd();

                    $('#order_button').html('开始搬砖');
                    order_data = e;

                    $('.pg-order').find(".item_imgurl").attr("src", e.item_imgurl);
                    $('.pg-order').find(".item_name").html(e.item_name);
                    $('.pg-order').find(".time").html(e.time);
                    $('.pg-order').find(".orderId").html(e.origin_orderId);
                    $('.pg-order').find(".total_amount").html(parseFloat(e.total_amount).toFixed(2));
                    $('.pg-order').find(".award_amount").html(parseFloat(e.award_amount).toFixed(2));
                    $('.pg-order').find(".award_total_amount").html((parseFloat(e.total_amount) + parseFloat(e.award_amount)).toFixed(2));


                    $('#get-order-button').show();
                    $('#pay-order-button').hide();

                    $('.pg-order').show();
                } else {
                    if (e.msg == "暂无订单") {
                        getOrder_timer = setTimeout(function () {
                            get_order();
                        }, getRandomInt(delay_start, delay_end) * 1000);
                    } else {
                        gdEnd();
                        tp(e.msg);
                        $('#order_button').html('继续搬砖');
                    }
                }

            })
        }


        var order_cancel = function () {
            v3api("order_cancel", {
                data: {
                    orderId: order_data.orderId
                }
            }, function (e) {
                $('.pg-order').fadeOut(100);
                tp(e.msg);
            })
        }
        var order_confirm = function () {
            var tune = 3;
            layer.open({
                type: 2,
                content: '远程主机正在分配',
                time: tune,
                shadeClose: false
            })

            setTimeout(function () {
                $('#get-order-button').hide();
                $('#pay-order-button').show();
            }, tune * 1000)


        }

        var pay_order = function () {
            //可以在这一步实现支付


            var tune = 5;
            layer.open({
                type: 2,
                content: '等待商家系统响应',
                time: tune,
                shadeClose: false
            })

            setTimeout(function () {
                v3api("order_confirm", {
                    error: 1,
                    data: {
                        orderId: order_data.orderId
                    }
                }, function (e) {
                    if (e.code != 1) {
                        tp(e.msg);
                        if (e.msg != "安全码错误") {
                            $('.pg-order').fadeOut(100);
                        }
                        return;
                    }


                    $('.pg-order').fadeOut(100);
                    tp('支付成功~');

                    show_orderList('task', 0);

                    $('.order_page').hide();
                    $('#newtip').show();
                })
            }, tune * 1000);



        }



        var get_order_total = function () {
            v3api("get_order_total", {
                data: {
                }
            }, function (e) {
                $('#total_amount').html('' + parseFloat(e.total_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#total_award_amount').html(parseFloat(e.total_award_amount).toFixed(2) + '');
                $('#total_award_number').html(e.total_award_number);
                $('#today_amount').html(parseFloat(e.today_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#today_number').html(e.today_number);
                $('#yesterday_amount').html(parseFloat(e.yesterday_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#yesterday_number').html(e.yesterday_number);


                $('#today_onetouch_amount').html(parseFloat(e.today_onetouch_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#total_onetouch_amount').html(parseFloat(e.total_onetouch_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
            })
        }
        get_order_total();


        //监听
        subscribeToCustomEvent(function (e) {
            if (e.detail.last_path == "/payway_add.aspx" && e.detail.new_path == "/order.aspx") {
                console.log('页面发生变化');
                switch_payment(true);
            }
        });

    </script>



    <style>
        .rotating-element {
            width: 12px;
            height: 12px;
            background-color: bisque;
            margin-right: 10px;
            animation: rotate 3s ease-in-out infinite;
        }

        @keyframes rotate {
            0%, 100% {
                transform: rotate(0deg); /* 起始和结束状态，不旋转 */
            }

            25% {
                transform: rotate(90deg); /* 旋转90度，慢 */
            }

            75% {
                transform: rotate(270deg); /* 旋转270度，快 */
            }
        }
    </style>
















    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 286px;
            padding: 0 5px;
            box-sizing: border-box;
            border-radius: 15px;
            background: linear-gradient(180deg, #EEC43A, #FEFBF0);
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: #fff;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }

            .pop-cpt .pop-cpt-con3 table {
                font-weight: bold;
                border-collapse: collapse;
            }

            .pop-cpt .pop-cpt-con3 th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                /*background: #0082f3;*/
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
                /*color:#000;*/
                border: solid 1px #ffd1bf;
                background: #FE6225;
                color: #f5ecdc;
                text-shadow: 2px 3px 2px #7d5909;
            }

            .pop-cpt .pop-cpt-con3 td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
                border: solid 1px #FE6225;
                color: #5a5b5c;
                text-shadow: 2px 3px 2px #5a5b5c40;
            }

        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
    <div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd">
                <a>
                    <div class="pop-cpt-tit">分享好友一起搬砖</div>
                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                        </div>
                        <div class="pop-cpt-con3" style="margin: 10 x 0;">
                            <table width="100%" border="1">
                                <tbody id="share_list">
                                    <tr class="firstRow">
                                        <th>新增下级数量</th>
                                        <th>奖励搬砖额度</th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                    <div class="pop-cpt-footer">



                        <a style="color: #000; border: 1px solid #ccc; display: inline-block; padding: 6px 28px; border-radius: 50px; font-weight: bold;"
                            href="partners_manage.aspx?top=1" target="_blank">立即分享</a>

                    </div>
                </a>
            </div>
        </div>
    </div>



    <script>
        var reward_number = '<%=uConfig.gd(userdt,"reward_amount") %>';
        var share_list = '<%=uConfig.stcdata("share_sd_number").Replace("\n", "##") %>'.split('##');
        for (var i = 0; i < share_list.length; i++) {
            var s = share_list[i].split('~');
            var finish = '<span style="font-size: 12px;background: #000;padding: 0 5px;border-radius: 10px;color: yellow;margin-left: 5px;">已达成</span>';
            if (parseInt(s[1]) > parseInt(reward_number)) {
                finish = '';
            }
            $('#share_list').append('<tr><td>' + s[0] + '个</td><td>' + s[1] + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">' + finish + '</td></tr>');
        }
    </script>

    <script>
        function startCountdown() {
            function startCountdown(element, initialDuration) {
                var progress = element.find(".countdown-progress");
                var countdownText = element.find(".countdown-text");
                var interval = 1000;
                var progressAngle, minutes, seconds;
                var duration = initialDuration;

                var countdownInterval = setInterval(function () {
                    progressAngle = (1 - (duration / initialDuration)) * 360;
                    //console.log('progressAngle', progressAngle, countdownText.html());
                    progress.css("transform", "rotate(" + progressAngle + "deg)");

                    minutes = Math.floor(duration / 60);
                    seconds = duration % 60;

                    countdownText.text(minutes.toString().padStart(2, '0') + ":" + seconds.toString().padStart(2, '0'));

                    duration--;

                    if (duration < 0) {
                        clearInterval(countdownInterval);
                    }
                }, interval);
            }

            // 调用倒计时函数并传入元素和初始倒计时时间
            $(".countdown-1").each(function () {
                var element = $(this);
                var initialDuration = parseInt(element.data("duration"), 10);
                if (initialDuration > 0) {
                    startCountdown(element, initialDuration);
                }
                //console.log('initialDuration', initialDuration);
            });
        }
    </script>

    <script>
        if (get_param('task') == "1") {
            if (get_param('payment_id') == "") {
                location.href = "order_new.aspx";
            } else {
                get_orderList({})
            }
        }
    </script>





</asp:Content>
