using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));
        pams.Add(new SqlParameter("@sd_valid_lendcount", uConfig.stcdata("sd_valid_lendcount")));

        string sql = string.Empty;


        string[] g, g2;

        //sql = " declare @share_table table(num int,reward_number int) ";
        sql = " declare @share_table table(num int,reward_number decimal(18,2)) ";
        g = uConfig.stcdata("share_sd_number").Replace("\n", "#").Split('#');
        for (int i = 0; i < g.Length; i++)
        {
            g2 = g[i].Split('~');
            if (g2.Length == 2)
            {
                sql += " insert into @share_table values(" + g2[0] + "," + g2[1] + ") ";
            }
        }


        //一级（直属）
        pmlist["agent_sql"] = "parentid=@userid";

        //三级
        pmlist["agent_sql"] = "(CASE WHEN CHARINDEX(@sid, ','+ISNULL(relaids,'')) > 0 THEN LEN(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,'')))) - LEN(REPLACE(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,''))), ',', '')) ELSE 999 END)".Replace("@sid", "'," + uConfig.p_uid + ",'");
        pmlist["agent_sql"] = "  id<>" + uConfig.p_uid + " and " + pmlist["agent_sql"] + "<4 and " + pmlist["agent_sql"] + ">0 ";


        //Response.Write(" sql = " + pmlist["agent_sql"]);
        //Response.End();

        sql += @" 








    declare @daily_share_number int
    declare @reward_number int
    declare @share_reward_amount decimal(18,2)

    set @daily_share_number=0
    set @reward_number=0  -- 邀请奖励次数
    set @share_reward_amount=0  -- 邀请奖励额度

    select @daily_share_number=count(0) from share_people with(nolock) where datediff(day,create_time,getdate())=0 and parentid=@userid and isvalid=1
    select top 1 @share_reward_amount=reward_number from @share_table where num<=@daily_share_number order by reward_number desc





select 
*,@reward_number as reward_number,@share_reward_amount as share_reward_amount
,isnull((case when DATEDIFF(DAY,order_time,GETDATE())=0 then task_daily_num else 0 end),0) as payment_number
,isnull((case when DATEDIFF(DAY,order_time,GETDATE())=0 then task_daily_amt else 0 end),0) as payment_amount 
from accounts with(nolock) where id=@userid

select top 1 * from payment_list pl with(nolock) where userid=@userid and state=1 order by pl.id 



declare @daily_number int
set @daily_number=0
select @daily_number=number from user_limit_number with(nolock) where userid=@userid and datediff(day,getdate(),update_time)=0


declare @daily_amount int
set @daily_amount=0
select @daily_amount=amount from user_limit_amount with(nolock) where userid=@userid and datediff(day,getdate(),update_time)=0

select @daily_number as daily_number,@daily_amount as daily_amount


-- 查询下级有效人数【3】
--select count(0) as number from accounts with(nolock) where {agent_sql} and trans_amount>=@sd_valid_money
select count(0) as number from accounts with(nolock) where {agent_sql} and isnull(onetouch_amount,0)>=@sd_valid_money

-- 查询出借中订单数
-- select count(0) as number from store_orders with(nolock) where (userid=@userid or parentid=@userid) and state=1000

-- 查询是否双倍刷单收益
select id from  [luckwheel_records] with(nolock) where userid=@userid and name='双倍刷单收益' and create_time>=DATEADD(HOUR,-24,GETDATE())

".Replace("{agent_sql}", pmlist["agent_sql"] + "");
        DataSet ds = db.getDataSet(sql, pams.ToArray());

        userdt = ds.Tables[0];


        Dictionary<string, object> temp_dic = chelper.getUserParames(uConfig.gd(userdt, "usertype"), uConfig.gd(userdt, "groupid"));
        pmlist["show_type"] = temp_dic["show_type"];
        pmlist["limit_number"] = temp_dic["limit_number"];
        pmlist["limit_amount"] = temp_dic["limit_amount"];
        pmlist["order_number"] = temp_dic["order_number"];
        pmlist["usertype"] = temp_dic["usertype"];


        pmlist["tp"] = Convert.ToBase64String(EncryptAesEcb(temp_dic["usertype"] + "", md5("userenc_" + uConfig.p_uid)));


        //Response.Write("Amount=" + uConfig.gd(userdt, "payment_amount", "0"));
        //Response.End();

        pmlist["number"] = Convert.ToInt32(pmlist["limit_number"]) - Convert.ToInt32(uConfig.gd(userdt, "payment_number", "0"));
        pmlist["amount"] = Convert.ToDouble(pmlist["limit_amount"]) - Convert.ToDouble(uConfig.gd(userdt, "payment_amount", "0"));




        //减去已用次数（额度）
        dt = ds.Tables[1];
        pmlist["payment_id"] = "";

        pmlist["payment_name"] = "";
        pmlist["payment_bankid"] = "";
        pmlist["payment_bankname"] = "";
        pmlist["payment_bankbranch"] = "";
        if (dt.Rows.Count > 0)
        {
            pmlist["payment_id"] = dt.Rows[0]["id"] + "";
            


            pmlist["payment_name"] = dt.Rows[0]["name"] + "";
            pmlist["payment_bankid"] = dt.Rows[0]["bankid"] + "";
            pmlist["payment_bankname"] = dt.Rows[0]["bankname"] + "";
            pmlist["payment_bankbranch"] = "";

            g = (pmlist["payment_bankname"] + "").Split(' ');
            if (g.Length > 0)
            {
                pmlist["payment_bankname"] = g[0];
            }
            if (g.Length > 1)
            {
                pmlist["payment_bankbranch"] = g[1];
            }

        }


        //手加次数（额度）
        dt = ds.Tables[2];
        if (dt.Rows.Count > 0)
        {
            pmlist["number"] = Convert.ToInt32(pmlist["number"]) + Convert.ToInt32(dt.Rows[0]["daily_number"]);
            pmlist["amount"] = Convert.ToInt32(pmlist["amount"]) + Convert.ToInt32(dt.Rows[0]["daily_amount"]);
        }


        //有效邀请人数
        dt = ds.Tables[3];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["number"] + "";
        }

        dt = ds.Tables[4];
        pmlist["is_double"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["is_double"] = "1";
        }



        //DataTable temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC"); ;
        //try
        //{
        //    temp_dt = selectDateTable(temp_dt, "id=" + uConfig.gd(userdt, "levelid"));
        //}
        //catch (Exception)
        //{
        //    temp_dt = new DataTable();
        //}
        //if (temp_dt.Rows.Count > 0)
        //{
        //    if (Convert.ToInt16(temp_dt.Rows[0]["minNumber"]) > Convert.ToInt16(pmlist["valid_user_number"]))
        //    {
        //        pmlist["valid_user_number"] = temp_dt.Rows[0]["minNumber"] + "";
        //    }

        //}










        //classList.DataSource = SortDataTable(selectDateTable(chelper.gdt("item_classList"), "state=1"), "sort_index ASC");
        //classList.DataBind();

        levels_list.DataSource = SortDataTable(chelper.gdt("levels_list"), "leftAmount ASC");
        levels_list.DataBind();

        //levels_list_gd.DataSource = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //levels_list_gd.DataBind();

        // 生成轮播数据
        GenerateCarouselData();
    }

    /// <summary>
    /// 生成随机轮播数据
    /// </summary>
    private void GenerateCarouselData()
    {
        Random rand = new Random();
        List<object> carouselData = new List<object>();


        string minAmountStr = uConfig.stcdata( "onetouch_minAmt");
        string maxAmountStr = uConfig.stcdata("onetouch_maxAmt");

        // 预定义的任务金额范围

        // 生成12条随机数据
        for (int i = 0; i < 12; i++)
        {
            // 生成随机用户名（数字格式：XX***XX）
            string username = GenerateRandomUsername(rand);

            // 随机选择任务金额
            decimal taskAmount = rand.Next(Convert.ToInt16(minAmountStr),Convert.ToInt16(maxAmountStr));

            // 根据任务金额计算收益（2.1%的比例）
            double profitRate = 0.021;
            decimal profit = Math.Round(taskAmount * (decimal)profitRate, 2);

            carouselData.Add(new
            {
                Username = username,
                Amount = taskAmount.ToString("F2"),
                Profit = profit.ToString("F2")
            });
        }

        // 绑定到Repeater
        CarouselRepeater.DataSource = carouselData;
        CarouselRepeater.DataBind();
    }

    /// <summary>
    /// 生成随机用户名（数字格式）
    /// </summary>
    /// <param name="rand">随机数生成器</param>
    /// <returns>格式如：89***12</returns>
    private string GenerateRandomUsername(Random rand)
    {
        // 生成前两位数字
        int prefix = rand.Next(10, 100);

        // 生成后两位数字
        int suffix = rand.Next(10, 100);

        return prefix+"***"+suffix;
    }


    public object positive_number(object numb)
    {
        object result = numb;

        try
        {
            if (Convert.ToDouble(result) < 0)
            {
                result = "0";
            }
        }
        catch (Exception)
        {
        }

        return result;
    }

}