<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ChangeAdminPassword.aspx.cs" Inherits="ChangeAdminPassword" %>

<!DOCTYPE html>
<html>
<head>
    <title>服务器密码修改工具</title>
    <style>
        .form-container { max-width: 500px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="password"] { width: 100%; padding: 8px; }
        button { background: #4CAF50; color: white; padding: 10px 15px; border: none; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Administrator密码修改</h2>
        <form id="form1" runat="server">
            <div class="form-group">
                <label>原密码：</label>
                <asp:TextBox ID="txtOldPwd" runat="server" TextMode="Password" />
            </div>
            <div class="form-group">
                <label>新密码：</label>
                <asp:TextBox ID="txtNewPwd" runat="server" TextMode="Password" />
            </div>
            <asp:Button ID="btnSubmit" runat="server" Text="提交修改" OnClick="btnSubmit_Click" />
            <asp:Label ID="lblResult" runat="server" ForeColor="Red" />
        </form>
    </div>
</body>
</html>
