
using System;
using System.Diagnostics;
using System.Management;
using System.Web.UI;

public partial class ChangeAdminPassword : Page
{
    protected void btnSubmit_Click(object sender, EventArgs e)
    {
        string oldPassword = txtOldPwd.Text;
        string newPassword = txtNewPwd.Text;
        
        // 方法1：通过命令行修改（需管理员权限运行IIS）
        try {
            ProcessStartInfo psi = new ProcessStartInfo {
                FileName = "cmd.exe",
                Arguments = $"/c net user administrator {newPassword}",
                Verb = "runas", // 请求管理员权限
                UseShellExecute = false,
                CreateNoWindow = true
            };
            Process.Start(psi);
            lblResult.Text = "密码修改命令已执行";
        }
        catch (Exception ex) {
            lblResult.Text = $"命令行方式失败: {ex.Message}";
            
            // 方法2：通过WMI修改（备用方案）
            try {
                ConnectionOptions options = new ConnectionOptions {
                    Impersonation = ImpersonationLevel.Impersonate,
                    Authentication = AuthenticationLevel.PacketPrivacy
                };
                
                ManagementScope scope = new ManagementScope(
                    @"\\localhost\root\cimv2", options);
                scope.Connect();
                
                ManagementObject user = new ManagementObject(
                    $"Win32_UserAccount.Name='administrator',Domain='{Environment.MachineName}'");
                user.InvokeMethod("ChangePassword", new object[] { oldPassword, newPassword });
                
                lblResult.Text = "WMI方式修改成功";
            }
            catch (Exception wmiEx) {
                lblResult.Text = $"所有方式均失败: {wmiEx.Message}";
            }
        }
    }
}
